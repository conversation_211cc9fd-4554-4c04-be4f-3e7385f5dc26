import { DBOS } from '@dbos-inc/dbos-sdk';
import { ChatService } from './core/ChatService';
import { GeminiLLMService } from './services/GeminiLLMService';
import { LLMServiceFactory } from './services/LLMService';
import { WebInterface } from './interfaces/WebInterface';
import { StreamingChatService } from './streaming';
import { SessionCleanupService } from './core/SessionCleanupService';
import { ForaChat } from './operations';
import config, { validateConfig } from './config';
import WebSocket from 'ws';
import http from 'http';
// Import operations to ensure workflow queues are initialized before DBOS launch
import './operations';

export class ForaChatApp {
  private chatService: ChatService;
  private webInterface: WebInterface;
  private streamingService: StreamingChatService;
  private server: http.Server | null = null;
  private wss: WebSocket.Server | null = null;
  private isInitialized: boolean = false;
  private quietMode: boolean = false;

  constructor(options: { quietMode?: boolean } = {}) {
    // Validate configuration
    validateConfig();

    // Set quiet mode
    this.quietMode = options.quietMode || false;

    // Register LLM providers
    LLMServiceFactory.register('gemini', () => new GeminiLLMService());

    // Initialize services
    const llmService = LLMServiceFactory.create('gemini');
    this.chatService = new ChatService(llmService);
    this.webInterface = new WebInterface(this.chatService);
    this.streamingService = new StreamingChatService(this.chatService);
  }

  private log(message: string): void {
    if (!this.quietMode) {
      console.log(message);
    }
    // In quiet mode, suppress all ForaChat application logs completely
  }



  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Configure DBOS
    DBOS.setConfig({
      name: "forachat",
      databaseUrl: config.database.url,
      userDbclient: "knex" as any,
      logLevel: this.quietMode ? "warn" : "info",
    });

    // Launch DBOS with the web interface
    await DBOS.launch({ expressApp: this.webInterface.getApp() });
    this.log("✅ DBOS Launched successfully");

    // Start session cleanup service (runs every hour)
    SessionCleanupService.startCleanupService(60);
    this.log("✅ Session cleanup service started");

    this.isInitialized = true;
  }

  async start(): Promise<void> {
    await this.initialize();

    const app = this.webInterface.getApp();

    // Create HTTP server
    this.server = http.createServer(app);

    // Create WebSocket server
    this.wss = new WebSocket.Server({ server: this.server });

    // Handle WebSocket connections
    this.wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
      this.handleWebSocketConnection(ws, req);
    });

    this.server.listen(config.port, () => {
      this.log(`🚀 ForaChat server is running on http://localhost:${config.port}`);
      this.log(`📊 Environment: ${config.environment}`);
      this.log(`🤖 LLM Provider: ${config.llm.model}`);
      this.log('\n📚 Available endpoints:');
      this.log('  GET  / - Web UI');
      this.log('  POST /chat - Send a message');
      this.log('  GET  /health - Health check');
      this.log('  POST /conversation/:id/message - Continue conversation');
      this.log('  GET  /conversation/:id - Get conversation history');
      this.log('  WebSocket - Real-time chat interface');
    });
  }

  getChatService(): ChatService {
    return this.chatService;
  }

  getWebInterface(): WebInterface {
    return this.webInterface;
  }

  getStreamingService(): StreamingChatService {
    return this.streamingService;
  }



  private async handleWebSocketConnection(ws: WebSocket, req: http.IncomingMessage): Promise<void> {
    try {
      // Parse cookies from WebSocket request
      const cookies = this.parseCookies(req.headers.cookie || '');
      let sessionId = cookies.forachat_session;
      let session = null;
      const userIdentifier = req.socket.remoteAddress || 'unknown';

      this.log(`🔗 WebSocket connection attempt from ${userIdentifier}, cookie sessionId: ${sessionId ? sessionId.substring(0, 8) + '...' : 'none'}`);

      if (sessionId) {
        // Try to get existing session
        const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
        session = await handle.getResult();

        if (session) {
          this.log(`✅ WebSocket reusing existing session ${sessionId.substring(0, 8)}... for user ${session.user_identifier}`);
        } else {
          this.log(`❌ WebSocket session ${sessionId.substring(0, 8)}... not found or expired, creating new session`);
        }
      } else {
        this.log(`🆕 WebSocket no session cookie found, creating new session for ${userIdentifier}`);
      }

      if (!session) {
        // Create new session for WebSocket
        sessionId = this.generateSessionId();

        const sessionRequest = {
          userIdentifier: `web_${userIdentifier}`,
          channel: 'web' as const,
          metadata: {
            userAgent: req.headers['user-agent'],
            ip: req.socket.remoteAddress
          }
        };

        const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await handle.getResult();
        sessionId = session.id;
        this.log(`🆕 Created new WebSocket session ${sessionId.substring(0, 8)}... for user ${session.user_identifier}`);
      } else {
        // Update session activity
        await DBOS.startWorkflow(ForaChat).updateSessionActivity(sessionId);
      }

      this.streamingService.createSession(sessionId, ws, session);
    } catch (error) {
      if (!this.quietMode) {
        console.error(`Error handling WebSocket connection: ${(error as Error).message}`);
      }
      ws.close(1011, 'Internal server error');
    }
  }

  private parseCookies(cookieHeader: string): Record<string, string> {
    const cookies: Record<string, string> = {};
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
    return cookies;
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  async shutdown(): Promise<void> {
    this.log('🛑 Shutting down ForaChat...');

    // Stop session cleanup service
    SessionCleanupService.stopCleanupService();

    // Close WebSocket server
    if (this.wss) {
      this.wss.close();
    }

    // Close HTTP server
    if (this.server) {
      this.server.close();
    }

    process.exit(0);
  }
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
